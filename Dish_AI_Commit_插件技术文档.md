# Dish AI Commit 插件技术文档

## 概述

Dish AI Commit 是一个功能强大的 VSCode 扩展，专门用于生成标准化的 Git/SVN 提交信息。该插件集成了多种 AI 服务提供商，支持多语言，并提供了丰富的配置选项和用户界面。

## 1. AI 设置部分 - Providers 架构

### 1.1 AI 提供商工厂 (AIProviderFactory)

**文件位置**: `src/ai/ai-provider-factory.ts`

#### 核心功能
- **提供商管理**: 统一管理所有 AI 服务提供商的实例创建和生命周期
- **缓存机制**: 实现 30 分钟的提供商实例缓存，提高性能
- **动态加载**: 根据配置动态创建对应的 AI 提供商实例

#### 支持的 AI 提供商
```typescript
// 支持的提供商列表
const supportedProviders = [
  "OpenAI",           // 标准 GPT 模型
  "Ollama",           // 本地部署模型
  "VS Code Provided", // VSCode 内置 AI
  "Zhipu",            // 智谱 AI 服务
  "DashScope",        // 阿里云通义平台
  "Doubao",           // 豆包 AI 平台
  "Gemini",           // Google Gemini 模型
  "Deepseek",         // Deepseek AI
  "Siliconflow",      // 硅基流动 API 服务
  "OpenRouter",       // OpenRouter 聚合 API 服务
  "PremAI",           // PremAI 服务
  "Together",         // Together AI
  "Anthropic",        // Claude 系列模型
  "Mistral",          // Mistral AI
  "Baidu Qianfan",    // 百度千帆
  "Azure OpenAI",     // Azure OpenAI 服务
  "Cloudflare",       // Cloudflare Workers AI
  "VertexAI",         // Google Vertex AI
  "Groq"              // Groq AI
];
```

#### 工厂模式实现
```typescript
public static getProvider(type?: string): AIProviderInterface {
  this.cleanStaleProviders(); // 清理过期实例
  
  const providerType = type || 
    ConfigurationManager.getInstance().getConfig("BASE_PROVIDER") || 
    AIProvider.OPENAI;

  let provider = this.providers.get(providerType);
  
  if (!provider) {
    // 根据类型创建对应的提供商实例
    switch (providerType.toLowerCase()) {
      case AIProvider.OPENAI:
        provider = new OpenAIProvider();
        break;
      case AIProvider.ZHIPU:
        provider = new ZhipuAIProvider();
        break;
      // ... 其他提供商
    }
    
    // 缓存实例
    this.providers.set(providerType, provider);
    this.providerTimestamps.set(providerType, Date.now());
  }
  
  return provider;
}
```

### 1.2 AI 提供商接口定义

**文件位置**: `src/ai/types.ts`

#### AIProvider 接口
```typescript
export interface AIProvider {
  // 核心功能
  generateCommit(params: AIRequestParams): Promise<AIResponse>;
  generateLayeredCommit?(params: AIRequestParams): Promise<LayeredCommitMessage>;
  generateCommitStream?(params: AIRequestParams): Promise<AsyncIterable<string>>;
  
  // 扩展功能
  generateCodeReview?(params: AIRequestParams): Promise<AIResponse>;
  generateBranchName?(params: AIRequestParams): Promise<AIResponse>;
  generateWeeklyReport(commits: string[], period: {...}): Promise<AIResponse>;
  generatePRSummary?(params: AIRequestParams, commitMessages: string[]): Promise<AIResponse>;
  
  // 服务管理
  isAvailable(): Promise<boolean>;
  refreshModels(): Promise<string[]>;
  getModels(): Promise<AIModel[]>;
  getName(): string;
  getId(): string;
  countTokens?(params: AIRequestParams): Promise<{ totalTokens: number }>;
}
```

#### AIModel 接口
```typescript
export interface AIModel {
  readonly id: string;              // 模型唯一标识符
  readonly name: string;            // 模型名称
  readonly maxTokens: {             // token 限制
    input: number;
    output: number;
  };
  readonly provider: {              // 提供者信息
    id: string;
    name: string;
  };
  readonly default?: boolean;       // 是否为默认模型
  readonly hidden?: boolean;        // 是否在界面上隐藏
  readonly capabilities?: {         // 模型特殊能力
    streaming?: boolean;
    functionCalling?: boolean;
  };
  readonly cost?: {                 // 费用信息
    input: number;
    output: number;
  };
}
```

### 1.3 配置管理

**文件位置**: `src/config/config-schema.ts`

#### 提供商配置结构
```typescript
// 基础配置
base: {
  language: "Simplified Chinese",    // 提交信息语言
  provider: "OpenAI",               // 默认 AI 提供商
  model: "gpt-3.5-turbo"           // 默认模型
}

// 提供商特定配置
providers: {
  openai: {
    apiKey: "",                     // API 密钥
    baseUrl: "https://api.openai.com/v1"
  },
  zhipu: {
    apiKey: ""                      // 智谱 AI API 密钥
  },
  ollama: {
    baseUrl: "http://localhost:11434"  // Ollama 本地服务地址
  }
  // ... 其他提供商配置
}
```

## 2. Generate Commit 业务逻辑

### 2.1 命令执行流程

**文件位置**: `src/commands/generate-commit-command.ts`

#### 核心执行流程
```typescript
async execute(resources: vscode.SourceControlResourceState[]): Promise<void> {
  // 1. 确认 AI 提供商服务条款
  if (!(await this.showConfirmAIProviderToS())) return;
  
  // 2. 处理配置
  const configResult = await this.handleConfiguration();
  if (!configResult) return;
  const { provider, model } = configResult;
  
  // 3. 获取选中文件
  const selectedFiles = this.getSelectedFiles(resources);
  if (!selectedFiles) {
    notify.info("no.files.selected");
    return;
  }
  
  // 4. 检测 SCM 提供商
  const scmProvider = await this.detectSCMProvider(selectedFiles);
  if (!scmProvider) return;
  
  // 5. 执行流式生成
  await ProgressHandler.withProgress(
    formatMessage("progress.generating.commit", [scmProvider.type.toLocaleUpperCase()]),
    (progress, token) => this.performStreamingGeneration(
      progress, token, provider, model, scmProvider, selectedFiles
    )
  );
}
```

#### 流式生成核心逻辑
```typescript
private async performStreamingGeneration(
  progress: vscode.Progress<{...}>,
  token: vscode.CancellationToken,
  provider: string,
  model: string,
  scmProvider: ISCMProvider,
  selectedFiles: string[]
) {
  // 1. 获取 diff 内容
  progress.report({ message: getMessage("progress.getting.diff") });
  const diffContent = await scmProvider.getDiff(selectedFiles);
  
  // 2. 准备请求参数
  progress.report({ message: getMessage("progress.preparing.request") });
  const systemPrompt = getSystemPrompt(tempParams);
  
  // 3. 构建上下文管理器
  const contextManager = await this._buildContextManager(
    selectedModel, systemPrompt, scmProvider, diffContent, configuration
  );
  
  // 4. 执行 AI 生成
  if (useFunctionCalling) {
    await this.performFunctionCallingGeneration(...);
  } else {
    await this.streamAndApplyMessage(...);
  }
  
  // 5. 分层提交处理（可选）
  if (configuration.features.commitFormat.enableLayeredCommit) {
    await this.performLayeredFileCommitGeneration(...);
  }
}
```

### 2.2 上下文管理

**文件位置**: `src/utils/context-manager.ts`

#### ContextManager 功能
- **智能截断**: 根据模型 token 限制智能截断上下文
- **优先级管理**: 不同类型的上下文有不同的优先级
- **重试机制**: 当请求过大时自动重试

#### 上下文构建
```typescript
private async _buildContextManager(
  selectedModel: AIModel,
  systemPrompt: string,
  scmProvider: ISCMProvider,
  diffContent: string,
  configuration: any
): Promise<ContextManager> {
  // 1. 获取所有上下文信息
  const currentInput = await this._getSCMInputContext(scmProvider);
  const { userCommits, repoCommits } = await this._getRecentCommits(scmProvider, ...);
  const reminder = this._getReminder(userCommits, repoCommits);
  
  // 2. 创建上下文管理器
  const contextManager = new ContextManager(
    selectedModel.maxTokens.input,
    TruncationStrategy.SMART
  );
  
  // 3. 添加不同优先级的上下文块
  contextManager.addBlock("system", systemPrompt, 1000);           // 最高优先级
  contextManager.addBlock("diff", diffContent, 800);               // 高优先级
  contextManager.addBlock("current_input", currentInput, 600);     // 中等优先级
  contextManager.addBlock("user_commits", userCommits, 400);       // 较低优先级
  contextManager.addBlock("repo_commits", repoCommits, 200);       // 最低优先级
  
  return contextManager;
}
```

### 2.3 提示语模板

**文件位置**: `src/prompt/generate-commit.ts`

#### 系统提示语生成
```typescript
export function generateCommitMessageSystemPrompt({
  config,
  vcsType,
}: SystemPromptParams) {
  const {
    base: { language },
    features: {
      commitFormat: { enableMergeCommit, enableEmoji, enableBody = true },
      commitMessage: { useRecentCommitsAsReference },
    },
  } = config;

  const VCSUpper = vcsType.toUpperCase();
  return `# ${VCSUpper} Commit Message Guide

## CORE INSTRUCTIONS

1. Determine the true intention of this commit based on the actual changes
2. Identify the modules/files that have been modified
3. Determine the type of modification
4. WRITE ALL CONTENT IN ${language} (except for technical terms and scope)
5. FOLLOW THE EXACT FORMAT TEMPLATE shown in examples
6. USE ENGLISH ONLY FOR SCOPE and technical terms
7. INCLUDE APPROPRIATE EMOJI when enabled (${enableEmoji ? "ENABLED" : "DISABLED"})
8. ${enableMergeCommit 
    ? "MERGE all changes into a SINGLE commit message" 
    : "CREATE SEPARATE commit messages for each file"}

## PROHIBITED ACTIONS (MUST NOT DO)

1. DO NOT include any explanations, greetings, or additional text
2. DO NOT write in English (except for technical terms and scope)
3. DO NOT add any formatting instructions or metadata
4. DO NOT include triple backticks (\`\`\`) in your output
5. DO NOT add any comments or questions
6. DO NOT deviate from the required format

## FORMAT TEMPLATE

${getMergeCommitsSection(enableMergeCommit, enableEmoji, enableBody)}

## TYPE DETECTION GUIDE
...`;
}
```

### 2.4 分层提交功能

**文件位置**: `src/prompt/layered-commit-file.ts`

#### 分层提交逻辑
```typescript
async performLayeredFileCommitGeneration(
  aiProvider: AIProvider,
  requestParams: AIRequestParams,
  scmProvider: ISCMProvider,
  selectedFiles: string[],
  token: vscode.CancellationToken,
  progress: vscode.Progress<{...}>
) {
  const fileDescriptions: { filePath: string; description: string }[] = [];
  
  // 为每个文件生成单独的描述
  for (const filePath of selectedFiles) {
    const fileDiff = await scmProvider.getDiff([filePath]);
    const filePrompt = getLayeredCommitFilePrompt({
      ...requestParams,
      changeFiles: [filePath],
      diff: fileDiff,
    });
    
    const description = await aiProvider.generateCommit({
      ...fileRequestParams,
      messages: [
        { role: "system", content: filePrompt },
        { role: "user", content: fileDiff }
      ],
    });
    
    fileDescriptions.push({
      filePath,
      description: description.content,
    });
  }
  
  // 显示分层提交详情
  if (fileDescriptions.length > 0) {
    await this._showLayeredCommitDetails(fileDescriptions);
  }
}
```

## 3. 界面设计

### 3.1 整体架构

**技术栈**:
- **前端**: React + TypeScript + Arco Design
- **构建工具**: Vite
- **通信**: VSCode Webview API

**文件结构**:
```
src/webview-ui/
├── src/
│   ├── pages/
│   │   ├── settings-page.tsx      # 设置页面主组件
│   │   └── setting/
│   │       ├── SettingsMenu.tsx   # 设置菜单
│   │       ├── SettingsContent.tsx # 设置内容
│   │       └── CodeIndexSettings.tsx # 代码索引设置
│   ├── App.tsx                    # 应用主组件
│   └── main.tsx                   # 入口文件
└── dist/                          # 构建输出
```

### 3.2 设置页面设计

**文件位置**: `src/webview-ui/src/pages/settings-page.tsx`

#### 主要功能模块
1. **AI 提供商配置**: 选择和配置不同的 AI 服务
2. **模型选择**: 为每个提供商选择合适的模型
3. **功能开关**: 控制各种功能的启用/禁用
4. **代码索引**: 管理本地代码索引和嵌入

#### 组件结构
```typescript
const SettingsPage: React.FC = () => {
  const {
    settingsSchema,      // 设置架构
    embeddingModels,     // 嵌入模型列表
    isLoading,          // 加载状态
    hasChanges,         // 是否有未保存的更改
    isIndexing,         // 是否正在索引
    indexingProgress,   // 索引进度
  } = useMessageHandler();

  return (
    <Layout style={{ height: "100vh" }}>
      <Layout.Sider width={280}>
        <SettingsMenu
          selectedMenuItemKey={selectedMenuItemKey}
          setSelectedMenuItemKey={setSelectedMenuItemKey}
          settingsSchema={settingsSchema}
        />
      </Layout.Sider>
      <Layout.Content>
        <PageHeader
          title="插件设置"
          extra={
            <ArcoButton
              type="primary"
              icon={<Save size={16} />}
              onClick={handleSave}
              disabled={!hasChanges || saveDisabled}
            >
              保存设置
            </ArcoButton>
          }
        />
        <SettingsContent
          selectedMenuItemKey={selectedMenuItemKey}
          settingsSchema={settingsSchema}
          onSettingChange={handleSettingChange}
          // ... 其他属性
        />
      </Layout.Content>
    </Layout>
  );
};
```

### 3.3 后端 Webview 提供者

**文件位置**: `src/webview/settings-view-provider.ts`

#### 核心功能
```typescript
export class SettingsViewProvider implements vscode.WebviewViewProvider {
  public async resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this._view = webviewView;

    // 配置 webview 选项
    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [
        vscode.Uri.joinPath(this._extensionUri, "webview-ui-dist"),
      ],
    };

    // 设置 HTML 内容
    webviewView.webview.html = this._htmlContentProvider.getWebviewContent(
      webviewView.webview
    );

    // 处理消息通信
    webviewView.webview.onDidReceiveMessage(
      async (message) => {
        await this._messageHandler.handleMessage(message, webviewView.webview);
      },
      null,
      this._disposables
    );
  }
}
```

### 3.4 消息处理机制

**文件位置**: `src/webview/handlers/settings-view-message-handler.ts`

#### 支持的消息类型
```typescript
public async handleMessage(message: any, webview: vscode.Webview): Promise<void> {
  switch (message.command) {
    case "loadSettings":
      // 加载设置配置
      await this.handleLoadSettings(webview);
      break;
      
    case "saveSettings":
      // 保存设置更改
      const newSettings = message.data;
      await this.handleSaveSettings(newSettings, webview);
      break;
      
    case "getModelsForProvider":
      // 获取指定提供商的模型列表
      const { providerId, providerContextKey } = message.data;
      await this.handleGetModelsForProvider(providerId, providerContextKey, webview);
      break;
      
    case "testConnection":
      // 测试 AI 服务连接
      const { service, url, key } = message.data;
      await this.handleTestConnection(service, url, key, webview);
      break;
      
    case "startIndexing":
      // 开始代码索引
      const { clearIndex } = message.data || {};
      this.startIndexing(0, webview, !!clearIndex);
      break;
  }
}
```

## 4. 配置和集成

### 4.1 VSCode 扩展配置

**文件位置**: `package.json`

#### 贡献点配置
```json
{
  "contributes": {
    "viewsContainers": {
      "activitybar": [{
        "id": "dish-ai-commitActivityBar",
        "title": "Dish Ai Commit Message",
        "icon": "/images/menu-icon.svg"
      }]
    },
    "views": {
      "dish-ai-commitActivityBar": [{
        "id": "dish-ai-commit.settingsView",
        "name": "插件设置",
        "type": "webview"
      }]
    },
    "commands": [
      {
        "command": "dish-ai-commit.generateCommitMessage",
        "title": "[Dish] Generate Commit with AI"
      },
      {
        "command": "dish-ai-commit.selectModel",
        "title": "[Dish] Select AI Model"
      }
    ],
    "menus": {
      "scm/title": [
        {
          "command": "dish-ai-commit.generateCommitMessage",
          "when": "scmProvider =~ /(git|svn)/",
          "group": "navigation@-3"
        }
      ]
    }
  }
}
```

### 4.2 配置项定义
```json
{
  "configuration": {
    "properties": {
      "dish-ai-commit.base.provider": {
        "type": "string",
        "default": "OpenAI",
        "enum": ["OpenAI", "Ollama", "VS Code Provided", "Zhipu", ...],
        "description": "AI provider / AI 提供商"
      },
      "dish-ai-commit.providers.openai.apiKey": {
        "type": "string",
        "default": "",
        "description": "OpenAI API Key / OpenAI API 密钥"
      }
    }
  }
}
```

## 5. 总结

Dish AI Commit 插件通过以下关键特性提供了强大的 AI 驱动提交信息生成能力：

1. **多提供商支持**: 集成了 19+ 种 AI 服务提供商
2. **智能上下文管理**: 通过 ContextManager 实现智能的上下文截断和优先级管理
3. **流式生成**: 支持实时流式生成提交信息，提供更好的用户体验
4. **分层提交**: 支持为每个文件生成详细的变更描述
5. **现代化界面**: 使用 React + Arco Design 构建的现代化设置界面
6. **灵活配置**: 丰富的配置选项满足不同用户需求

该插件的架构设计充分考虑了可扩展性、性能和用户体验，为开发者提供了高效的 Git/SVN 提交信息生成解决方案。
